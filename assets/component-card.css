.card-wrapper {
  color: inherit;
  height: 100%;
  position: relative;
  text-decoration: none;
}

.card {
  text-decoration: none;
  text-align: var(--text-alignment);
}

.card:not(.ratio) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card.card--horizontal {
  --text-alignment: left;
  --image-padding: 0rem;
  flex-direction: row;
  align-items: flex-start;
  gap: 1.5rem;
}

.card--horizontal.ratio:before {
  padding-bottom: 0;
}

.card--card.card--horizontal {
  padding: 1.2rem;
}

.card--card.card--horizontal.card--text {
  column-gap: 0;
}

.card--card {
  height: 100%;
}

.card--card,
.card--standard .card__inner {
  position: relative;
  box-sizing: border-box;
  border-radius: var(--border-radius);
  border: var(--border-width) solid rgba(var(--color-foreground), var(--border-opacity));
}

.card--card:after,
.card--standard .card__inner:after {
  content: '';
  position: absolute;
  z-index: -1;
  width: calc(var(--border-width) * 2 + 100%);
  height: calc(var(--border-width) * 2 + 100%);
  top: calc(var(--border-width) * -1);
  left: calc(var(--border-width) * -1);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius)
    rgba(var(--color-shadow), var(--shadow-opacity));
}

/* Needed for gradient continuity with or without animation, the transform scopes the gradient to its container which happens already when animation are turned on */
.card--card.gradient,
.card__inner.gradient {
  transform: perspective(0);
}

/* Needed for gradient continuity with or without animation so that transparent PNG images come up as we would expect */
.card__inner.color-scheme-1 {
  background: transparent;
}

.card .card__inner .card__media {
  overflow: hidden;
  /* Fix for Safari border bug on hover */
  z-index: 0;
  border-radius: calc(var(--border-radius) - var(--border-width) - var(--image-padding));
}

.card--card .card__inner .card__media {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.card--standard.card--text {
  background-color: transparent;
}

.card-information {
  text-align: var(--text-alignment);
}

.card__media,
.card .media {
  bottom: 0;
  position: absolute;
  top: 0;
}

.card .media {
  width: 100%;
}

.card__media {
  margin: var(--image-padding);
  width: calc(100% - 2 * var(--image-padding));
}

.card--standard .card__media {
  margin: var(--image-padding);
}

.card__inner {
  width: 100%;
}

.card--media .card__inner .card__content {
  position: relative;
  padding: calc(var(--image-padding) + 1rem);
}

.card__content {
  display: grid;
  grid-template-rows: minmax(0, 1fr) max-content minmax(0, 1fr);
  padding: 1rem;
  width: 100%;
  flex-grow: 1;
}

.card__content--auto-margins {
  grid-template-rows: minmax(0, auto) max-content minmax(0, auto);
}

.card__information {
  grid-row-start: 2;
  padding: 1.3rem 1rem;
}

.card:not(.ratio) > .card__content {
  grid-template-rows: max-content minmax(0, 1fr) max-content auto;
}

.card-information .card__information-volume-pricing-note {
  margin-top: 0.6rem;
  line-height: calc(0.5 + 0.4 / var(--font-body-scale));
  color: rgba(var(--color-foreground), 0.75);
}

.card__information-volume-pricing-note--button,
.card__information-volume-pricing-note--button.quantity-popover__info-button--icon-with-label {
  position: relative;
  z-index: 1;
  cursor: pointer;
  padding: 0;
  margin: 0;
  text-align: var(--text-alignment);
  min-width: auto;
}

.card__information-volume-pricing-note--button:hover {
  text-decoration: underline;
}

.card__information-volume-pricing-note--button + .global-settings-popup.quantity-popover__info {
  transform: initial;
  top: auto;
  bottom: 4rem;
  max-width: 20rem;
  width: calc(95% + 2rem);
}

.card__information-volume-pricing-note--button + .global-settings-popup.quantity-popover__info span:first-of-type {
  padding-right: 0.3rem;
}

.card__information-volume-pricing-note--button-right + .global-settings-popup.quantity-popover__info {
  right: 0;
  left: auto;
}

.card__information-volume-pricing-note--button-center + .global-settings-popup.quantity-popover__info {
  left: 50%;
  transform: translate(-50%);
}

.card__information-volume-pricing-note--button + .global-settings-popup.quantity-popover__info .quantity__rules {
  text-align: left;
}

@media screen and (min-width: 990px) {
  .grid--6-col-desktop .card__content quick-add-bulk .quantity {
    width: auto;
  }

  .grid--6-col-desktop .card__content quick-add-bulk .quantity__button {
    width: calc(3rem / var(--font-body-scale));
  }

  .grid--6-col-desktop .card__information-volume-pricing-note--button + .global-settings-popup.quantity-popover__info {
    left: 50%;
    transform: translate(-50%);
    width: calc(100% + var(--border-width) + 3.5rem);
  }

  .grid--6-col-desktop
    .card--standard
    .card__information-volume-pricing-note--button
    + .global-settings-popup.quantity-popover__info {
    width: calc(100% + var(--border-width) + 1rem);
  }
}

@media screen and (max-width: 749px) {
  .grid--2-col-tablet-down .card__content quick-add-bulk .quantity__button {
    width: calc(3.5rem / var(--font-body-scale));
  }

  .grid--2-col-tablet-down
    .card--card
    .card__information-volume-pricing-note--button
    + .global-settings-popup.quantity-popover__info,
  .grid--2-col-tablet-down
    .card--standard
    .card__information-volume-pricing-note--button
    + .global-settings-popup.quantity-popover__info {
    left: 50%;
    transform: translate(-50%);
  }

  .grid--2-col-tablet-down
    .card--standard
    .card__information-volume-pricing-note--button
    + .global-settings-popup.quantity-popover__info {
    width: 100%;
  }

  .grid--2-col-tablet-down
    .card--card
    .card__information-volume-pricing-note--button
    + .global-settings-popup.quantity-popover__info {
    width: calc(100% + var(--border-width) + 4rem);
  }

  .grid--2-col-tablet-down .card__content quick-add-bulk .quantity {
    width: auto;
  }
}

.card-information quantity-popover volume-pricing {
  margin-top: 0;
}

@media screen and (max-width: 989px) {
  .card-information quantity-popover .quantity__rules ~ volume-pricing {
    margin-top: 0;
  }

  .card-information quantity-popover volume-pricing {
    margin-top: 4.2rem;
  }
}

@media screen and (min-width: 750px) {
  .card__information {
    padding-bottom: 1.7rem;
    padding-top: 1.7rem;
  }
}

.card__badge {
  align-self: flex-end;
  grid-row-start: 3;
  justify-self: flex-start;
}

.card__badge.top {
  align-self: flex-start;
  grid-row-start: 1;
}

.card__badge.right {
  justify-self: flex-end;
}

.card:not(.card--horizontal) > .card__content > .card__badge {
  margin: 1.3rem;
}

.card__media .media img {
  height: 100%;
  object-fit: cover;
  object-position: center center;
  width: 100%;
}

.card__inner:not(.ratio) > .card__content {
  height: 100%;
}

.card__heading {
  margin-top: 0;
  margin-bottom: 0;
}

.card__heading:last-child {
  margin-bottom: 0;
}

.card--horizontal .card__heading,
.card--horizontal .price__container .price-item,
.card--horizontal__quick-add {
  font-size: calc(var(--font-heading-scale) * 1.2rem);
}

.card--horizontal
  .card-information
  > *:not(.visually-hidden:first-child)
  + *:not(.rating):not(.card__information-volume-pricing-note) {
  margin-top: 0;
}

.card--horizontal__quick-add:before {
  box-shadow: none;
}

@media only screen and (min-width: 750px) {
  .card--horizontal .card__heading,
  .card--horizontal .price__container .price-item,
  .card--horizontal__quick-add {
    font-size: calc(var(--font-heading-scale) * 1.3rem);
  }
}

.card--card.card--media > .card__content {
  margin-top: calc(0rem - var(--image-padding));
}

.card--standard.card--text a::after,
.card--card .card__heading a::after {
  bottom: calc(var(--border-width) * -1);
  left: calc(var(--border-width) * -1);
  right: calc(var(--border-width) * -1);
  top: calc(var(--border-width) * -1);
}

.card__heading a::after {
  bottom: 0;
  content: '';
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
}

.card__heading a:after {
  outline-offset: 0.3rem;
}

.card__heading a:focus:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
}

.card__heading a:focus-visible:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
}

.card__heading a:focus:not(:focus-visible):after {
  box-shadow: none;
  outline: 0;
}

.card__heading a:focus {
  box-shadow: none;
  outline: 0;
}

@media screen and (min-width: 990px) {
  .card .media.media--hover-effect > img:only-child,
  .card-wrapper .media.media--hover-effect > img:only-child {
    transition: transform var(--duration-long) ease;
  }

  .card:hover .media.media--hover-effect > img:first-child:only-child,
  .card-wrapper:hover .media.media--hover-effect > img:first-child:only-child {
    transform: scale(1.03);
  }

  .card-wrapper:hover .media.media--hover-effect > img:first-child:not(:only-child) {
    opacity: 0;
  }

  .card-wrapper:hover .media.media--hover-effect > img + img {
    opacity: 1;
    transition: transform var(--duration-long) ease;
    transform: scale(1.03);
  }

  .underline-links-hover:hover a {
    text-decoration: underline;
    text-underline-offset: 0.3rem;
  }
}

.card--standard.card--media .card__inner .card__information,
.card--standard.card--text:not(.card--horizontal) > .card__content .card__heading:not(.card__heading--placeholder),
.card--standard:not(.card--horizontal) > .card__content .card__badge,
.card--standard.card--text.article-card > .card__content .card__information,
.card--standard > .card__content .card__caption {
  display: none;
}

.card--standard:not(.card--horizontal) .placeholder-svg {
  width: 100%;
}

.card--standard > .card__content {
  padding: 0;
}

.card--standard > .card__content .card__information {
  padding-left: 0;
  padding-right: 0;
}

.card--card.card--media .card__inner .card__information,
.card--card.card--text .card__inner,
.card--card.card--media > .card__content .card__badge {
  display: none;
}

.card--horizontal .card__badge,
.card--horizontal.card--text .card__inner {
  display: none;
}

.card--extend-height {
  height: 100%;
}

.card--extend-height.card--standard.card--text,
.card--extend-height.card--media {
  display: flex;
  flex-direction: column;
}

.card--extend-height.card--standard.card--text .card__inner,
.card--extend-height.card--media .card__inner {
  flex-grow: 1;
}

.card .icon-wrap {
  margin-left: 0.8rem;
  white-space: nowrap;
  transition: transform var(--duration-short) ease;
  overflow: hidden;
}

.card-information > * + * {
  margin-top: 0.5rem;
}

.card-information {
  width: 100%;
}

.card-information > * {
  line-height: calc(1 + 0.4 / var(--font-body-scale));
  color: rgb(var(--color-foreground));
}

.card-information > .price {
  color: rgb(var(--color-foreground));
}

.card--horizontal .card-information > .price {
  color: rgba(var(--color-foreground), 0.75);
}

.card-information > .rating {
  margin-top: 0.4rem;
}

/* Specificity needed due to the changes below */
.card-information
  > *:not(.visually-hidden:first-child)
  + quantity-popover:not(.rating):not(.card__information-volume-pricing-note),
.card-information .card__information-volume-pricing-note.card__information-volume-pricing-note--button {
  margin-top: 0;
}

.card-information > *:not(.visually-hidden:first-child) + *:not(.rating):not(.card__information-volume-pricing-note) {
  margin-top: 0.7rem;
}

.card-information .caption {
  letter-spacing: 0.07rem;
}

.card-article-info {
  margin-top: 1rem;
}

/* Card Shapes */

.card--shape .card__content {
  padding-top: 0;
}

.card--shape.card--standard:not(.card--text) .card__inner {
  border: 0;
  /* Border is not currently compatible with image shapes for standard cards. */
  background-color: transparent;
  filter: drop-shadow(
    var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius)
      rgba(var(--color-shadow), var(--shadow-opacity))
  );
}

.card--shape.card--standard:not(.card--text) .card__inner:after {
  display: none;
}

.grid__item:nth-child(2n) .shape--blob {
  clip-path: polygon(var(--shape--blob-2));
}

.grid__item:nth-child(3n) .shape--blob {
  clip-path: polygon(var(--shape--blob-3));
}

.grid__item:nth-child(4n) .shape--blob {
  clip-path: polygon(var(--shape--blob-4));
}

.grid__item:nth-child(5n) .shape--blob {
  clip-path: polygon(var(--shape--blob-5));
}

.grid__item:nth-child(7n) .shape--blob {
  clip-path: polygon(var(--shape--blob-6));
}

.grid__item:nth-child(8n) .shape--blob {
  clip-path: polygon(var(--shape--blob-1));
}

/* Card Shape Hover Rules */

@media (prefers-reduced-motion: no-preference) {
  .product-card-wrapper .shape--round {
    transition: clip-path var(--duration-long) ease;
  }

  .product-card-wrapper:hover .shape--round {
    clip-path: ellipse(47% 47% at 50% 50%);
  }

  .product-card-wrapper .shape--blob {
    transition: clip-path var(--duration-long) ease-in-out;
  }

  .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-5));
  }

  .grid__item:nth-child(2n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-6));
  }

  .grid__item:nth-child(3n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-1));
  }

  .grid__item:nth-child(4n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-2));
  }

  .grid__item:nth-child(5n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-3));
  }

  .grid__item:nth-child(7n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-4));
  }

  .grid__item:nth-child(8n) .product-card-wrapper:hover .shape--blob {
    clip-path: polygon(var(--shape--blob-5));
  }
}

/* Professional Tour Card Enhancements */

/* Card layout improvements for better spacing */
.card-wrapper .card__information {
  padding: 1rem 1rem 0.5rem 1rem;
}

.card-wrapper .card__content {
  padding-bottom: 1rem;
}

/* Mobile-first approach - very compact by default */
@media screen and (max-width: 390px) {
  /* iPhone 12 mini and smaller */
  .card-wrapper .card__information {
    padding: 0.4rem 0.5rem 0.2rem 0.5rem;
  }

  .card__professional-reviews {
    margin: 0.2rem 0;
    padding: 0.1rem 0;
  }

  .rating--professional .rating-star {
    font-size: 0.8rem;
    margin-right: 0.15rem;
  }

  .rating--professional + .rating-text {
    font-size: 0.75rem;
    gap: 0.15rem;
  }

  .rating--professional + .rating-text .rating-count {
    font-size: 0.7rem;
  }

  .card__price-section {
    margin: 0.3rem 0;
    padding: 0.2rem 0;
  }

  .price--prominent {
    font-size: 1rem;
  }

  .price--prominent .price__regular,
  .price--prominent .price__sale {
    font-size: 1rem;
  }

  .price--prominent .price__compare-at {
    font-size: 0.8rem;
  }

  .button--view-details {
    padding: 0.4rem 0.6rem;
    font-size: 0.85rem;
    gap: 0.2rem;
  }

  .button--view-details .icon-arrow {
    width: 0.7rem;
    height: 0.7rem;
  }

  .card__view-details {
    margin: 0.25rem 0 0.15rem 0;
  }

  .rating--default .rating-star {
    font-size: 0.8rem;
    letter-spacing: 0.03rem;
  }

  .card__heading {
    font-size: 1rem;
    line-height: 1.1;
    margin-bottom: 0.2rem;
  }
}

/* Professional Review System */
.card__professional-reviews {
  margin: 0.8rem 0;
  padding: 0.5rem 0;
}

.rating--professional {
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;
}

.rating--professional .rating-star {
  font-size: 1.3rem;
  color: #ffc107;
  margin-right: 0.4rem;
}

.rating--professional + .rating-text {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 1.1rem;
  color: rgba(var(--color-foreground), 0.8);
  margin: 0;
}

.rating--professional + .rating-text .rating-count {
  font-size: 1rem;
  color: rgba(var(--color-foreground), 0.6);
  font-weight: 400;
}

/* Enhanced Price Section */
.card__price-section {
  margin: 1rem 0;
  padding: 0.8rem 0;
  border-top: 1px solid rgba(var(--color-foreground), 0.08);
}

.price--prominent {
  font-size: 1.6rem;
  font-weight: 600;
  color: rgb(var(--color-foreground));
}

.price--prominent .price__regular {
  font-size: 1.6rem;
}

.price--prominent .price__sale {
  font-size: 1.6rem;
  color: rgb(var(--color-base-accent-1));
}

.price--prominent .price__compare-at {
  font-size: 1.2rem;
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
}

/* Professional View Details Button */
.card__view-details {
  margin: 0.8rem 0 0.5rem 0;
}

.button--view-details {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.7rem 1.2rem;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  border: 1.5px solid rgb(var(--color-base-accent-1));
  background-color: transparent;
  color: rgb(var(--color-base-accent-1));
  border-radius: 6px;
  transition: all 0.25s ease;
  width: 100%;
  text-align: center;
  min-height: auto;
}

.button--view-details:hover {
  background-color: rgb(var(--color-base-accent-1));
  color: rgb(var(--color-base-background-1));
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(var(--color-base-accent-1), 0.25);
}

.button--view-details .icon-arrow {
  width: 1.2rem;
  height: 1.2rem;
  transition: transform 0.25s ease;
}

.button--view-details:hover .icon-arrow {
  transform: translateX(3px);
}

/* Default rating stars styling */
.rating--default .rating-star {
  --color-rating-star: #ffc107;
  position: relative;
  font-family: Times, serif;
  font-size: 1.3rem;
  line-height: 1;
  letter-spacing: 0.08rem;
}

.rating--default .rating-star::before {
  content: "★★★★★";
  background: linear-gradient(
    90deg,
    var(--color-rating-star) var(--percent),
    rgba(0, 0, 0, 0.15) var(--percent)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Ensure proper star display for all ratings */
.card__professional-reviews .rating-star {
  --color-rating-star: #ffc107;
}

/* Extra Small Mobile (max-width: 480px) */
@media screen and (max-width: 480px) {
  .card-wrapper .card__information {
    padding: 0.5rem 0.6rem 0.3rem 0.6rem;
  }

  .card-wrapper .card__content {
    padding-bottom: 0.6rem;
  }

  .card__professional-reviews {
    margin: 0.3rem 0;
    padding: 0.2rem 0;
  }

  .rating--professional .rating-star {
    font-size: 0.9rem;
    margin-right: 0.2rem;
  }

  .rating--professional + .rating-text {
    font-size: 0.8rem;
    gap: 0.2rem;
  }

  .rating--professional + .rating-text .rating-count {
    font-size: 0.75rem;
  }

  .card__price-section {
    margin: 0.4rem 0;
    padding: 0.3rem 0;
    border-top: 1px solid rgba(var(--color-foreground), 0.06);
  }

  .price--prominent {
    font-size: 1.1rem;
    font-weight: 600;
  }

  .price--prominent .price__regular,
  .price--prominent .price__sale {
    font-size: 1.1rem;
  }

  .price--prominent .price__compare-at {
    font-size: 0.9rem;
  }

  .button--view-details {
    padding: 0.45rem 0.7rem;
    font-size: 0.9rem;
    gap: 0.25rem;
    border-radius: 4px;
    border-width: 1px;
  }

  .button--view-details .icon-arrow {
    width: 0.8rem;
    height: 0.8rem;
  }

  .card__view-details {
    margin: 0.3rem 0 0.2rem 0;
  }

  .rating--default .rating-star {
    font-size: 0.9rem;
    letter-spacing: 0.04rem;
  }
}

/* Small Mobile (481px - 749px) */
@media screen and (min-width: 481px) and (max-width: 749px) {
  .card-wrapper .card__information {
    padding: 0.6rem 0.7rem 0.4rem 0.7rem;
  }

  .card__professional-reviews {
    margin: 0.4rem 0;
    padding: 0.3rem 0;
  }

  .rating--professional .rating-star {
    font-size: 1rem;
    margin-right: 0.3rem;
  }

  .rating--professional + .rating-text {
    font-size: 0.9rem;
    gap: 0.3rem;
  }

  .rating--professional + .rating-text .rating-count {
    font-size: 0.8rem;
  }

  .card__price-section {
    margin: 0.5rem 0;
    padding: 0.4rem 0;
  }

  .price--prominent {
    font-size: 1.2rem;
    font-weight: 600;
  }

  .price--prominent .price__regular,
  .price--prominent .price__sale {
    font-size: 1.2rem;
  }

  .price--prominent .price__compare-at {
    font-size: 1rem;
  }

  .button--view-details {
    padding: 0.5rem 0.8rem;
    font-size: 1rem;
    gap: 0.3rem;
    border-radius: 5px;
  }

  .button--view-details .icon-arrow {
    width: 0.9rem;
    height: 0.9rem;
  }

  .card__view-details {
    margin: 0.4rem 0 0.3rem 0;
  }

  .rating--default .rating-star {
    font-size: 1rem;
    letter-spacing: 0.05rem;
  }
}

/* Tablet Portrait (750px - 989px) */
@media screen and (min-width: 750px) and (max-width: 989px) {
  .card__professional-reviews {
    margin: 0.7rem 0;
    padding: 0.5rem 0;
  }

  .rating--professional .rating-star {
    font-size: 1.2rem;
    margin-right: 0.4rem;
  }

  .rating--professional + .rating-text {
    font-size: 1.05rem;
    gap: 0.4rem;
  }

  .rating--professional + .rating-text .rating-count {
    font-size: 0.95rem;
  }

  .card__price-section {
    margin: 0.9rem 0;
    padding: 0.7rem 0;
  }

  .price--prominent {
    font-size: 1.5rem;
  }

  .price--prominent .price__regular,
  .price--prominent .price__sale {
    font-size: 1.5rem;
  }

  .price--prominent .price__compare-at {
    font-size: 1.15rem;
  }

  .button--view-details {
    padding: 0.65rem 1.1rem;
    font-size: 1.15rem;
    gap: 0.45rem;
  }

  .button--view-details .icon-arrow {
    width: 1.1rem;
    height: 1.1rem;
  }

  .card__view-details {
    margin: 0.7rem 0 0.5rem 0;
  }

  .rating--default .rating-star {
    font-size: 1.2rem;
    letter-spacing: 0.07rem;
  }
}

/* Large Tablet/Small Desktop (990px - 1199px) */
@media screen and (min-width: 990px) and (max-width: 1199px) {
  .card__professional-reviews {
    margin: 0.8rem 0;
    padding: 0.6rem 0;
  }

  .rating--professional .rating-star {
    font-size: 1.25rem;
  }

  .rating--professional + .rating-text {
    font-size: 1.08rem;
  }

  .price--prominent {
    font-size: 1.55rem;
  }

  .price--prominent .price__regular,
  .price--prominent .price__sale {
    font-size: 1.55rem;
  }

  .button--view-details {
    padding: 0.68rem 1.15rem;
    font-size: 1.18rem;
  }

  .rating--default .rating-star {
    font-size: 1.25rem;
  }
}

/* Large Desktop (1200px+) */
@media screen and (min-width: 1200px) {
  .card__professional-reviews {
    margin: 0.8rem 0;
    padding: 0.6rem 0;
  }

  .rating--professional .rating-star {
    font-size: 1.3rem;
  }

  .rating--professional + .rating-text {
    font-size: 1.1rem;
  }

  .price--prominent {
    font-size: 1.6rem;
  }

  .button--view-details {
    padding: 0.7rem 1.2rem;
    font-size: 1.2rem;
  }

  .rating--default .rating-star {
    font-size: 1.3rem;
  }
}

/* Focus states for accessibility */
.button--view-details:focus-visible {
  outline: 2px solid rgb(var(--color-base-accent-1));
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
.card__professional-reviews,
.card__price-section,
.card__view-details {
  transition: opacity 0.2s ease;
}

/* Ensure proper spacing between elements */
.card-information > .card__professional-reviews + .card__price-section {
  margin-top: 0.5rem;
}

.card-information > .card__price-section + .card__view-details {
  margin-top: 0.3rem;
}

/* Grid responsive adjustments */
@media screen and (max-width: 749px) {
  /* 2 columns on mobile - make cards more compact */
  .grid--2-col-tablet-down .card-wrapper .card__information {
    padding: 0.4rem 0.5rem 0.3rem 0.5rem;
  }

  .grid--2-col-tablet-down .card__professional-reviews {
    margin: 0.3rem 0;
  }

  .grid--2-col-tablet-down .card__price-section {
    margin: 0.4rem 0;
  }

  .grid--2-col-tablet-down .card__view-details {
    margin: 0.3rem 0 0.2rem 0;
  }

  /* Extra compact for very small screens */
  .grid--2-col-tablet-down .card__heading {
    font-size: 1.1rem;
    line-height: 1.2;
    margin-bottom: 0.3rem;
  }

  /* Reduce card padding overall */
  .card-wrapper .card {
    margin: 0.5rem 0;
  }

  .card__content {
    padding: 0.5rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  /* 3-4 columns on tablet - balanced spacing */
  .grid--3-col-tablet .card-wrapper .card__information,
  .grid--4-col-desktop .card-wrapper .card__information {
    padding: 0.9rem 0.9rem 0.5rem 0.9rem;
  }
}

@media screen and (min-width: 990px) {
  /* 4+ columns on desktop - full spacing */
  .grid--4-col-desktop .card-wrapper .card__information,
  .grid--5-col-desktop .card-wrapper .card__information {
    padding: 1rem 1rem 0.6rem 1rem;
  }

  /* Hover effects for desktop */
  .card-wrapper:hover .card__professional-reviews,
  .card-wrapper:hover .card__price-section {
    transform: translateY(-1px);
  }

  .card-wrapper:hover .button--view-details {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(var(--color-base-accent-1), 0.3);
  }
}
