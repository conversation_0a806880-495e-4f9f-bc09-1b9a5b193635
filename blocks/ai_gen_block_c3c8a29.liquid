{% doc %}
  @prompt
    Create a contact form section with customer name field, email field, message textarea, and submit button. The form should be elegant and wedding-themed with proper validation. Include section settings for form title, description text, button text customization, and styling options. Make it responsive and ensure the form submits customer information properly. Add proper form handling and success message display.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .wedding-contact-form-{{ ai_gen_id }} {
    max-width: 100%;
    margin: 0 auto;
    padding: {{ block.settings.form_padding }}px;
    background-color: {{ block.settings.form_background }};
    border-radius: {{ block.settings.form_border_radius }}px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .wedding-contact-form__header-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 30px;
  }

  .wedding-contact-form__title-{{ ai_gen_id }} {
    font-size: {{ block.settings.title_size }}px;
    color: {{ block.settings.title_color }};
    margin-bottom: 15px;
    font-weight: 600;
  }

  .wedding-contact-form__description-{{ ai_gen_id }} {
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    line-height: 1.5;
  }

  .wedding-contact-form__fields-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .wedding-contact-form__field-{{ ai_gen_id }} {
    display: flex;
    flex-direction: column;
  }

  .wedding-contact-form__label-{{ ai_gen_id }} {
    margin-bottom: 8px;
    font-weight: 500;
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
  }

  .wedding-contact-form__input-{{ ai_gen_id }},
  .wedding-contact-form__textarea-{{ ai_gen_id }} {
    padding: 12px 16px;
    border: 1px solid {{ block.settings.input_border_color }};
    border-radius: {{ block.settings.input_border_radius }}px;
    background-color: {{ block.settings.input_background }};
    color: {{ block.settings.text_color }};
    font-size: {{ block.settings.text_size }}px;
    transition: border-color 0.3s, box-shadow 0.3s;
  }

  .wedding-contact-form__input-{{ ai_gen_id }}:focus,
  .wedding-contact-form__textarea-{{ ai_gen_id }}:focus {
    outline: none;
    border-color: {{ block.settings.accent_color }};
    box-shadow: 0 0 0 2px {{ block.settings.accent_color }}20;
  }

  .wedding-contact-form__textarea-{{ ai_gen_id }} {
    min-height: 150px;
    resize: vertical;
  }

  .wedding-contact-form__button-{{ ai_gen_id }} {
    margin-top: 10px;
    padding: 14px 28px;
    background-color: {{ block.settings.button_background }};
    color: {{ block.settings.button_text_color }};
    border: none;
    border-radius: {{ block.settings.button_border_radius }}px;
    font-size: {{ block.settings.text_size }}px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
    align-self: {{ block.settings.button_alignment }};
  }

  .wedding-contact-form__button-{{ ai_gen_id }}:hover {
    background-color: {{ block.settings.button_hover_background }};
  }

  .wedding-contact-form__success-{{ ai_gen_id }} {
    margin-top: 20px;
    padding: 15px;
    background-color: {{ block.settings.success_background }};
    color: {{ block.settings.success_text_color }};
    border-radius: 6px;
    text-align: center;
    display: none;
  }

  .wedding-contact-form__error-{{ ai_gen_id }} {
    color: {{ block.settings.error_color }};
    font-size: 14px;
    margin-top: 5px;
    display: none;
  }

  .wedding-contact-form__divider-{{ ai_gen_id }} {
    height: 1px;
    background: linear-gradient(to right, transparent, {{ block.settings.accent_color }}40, transparent);
    margin: 15px 0 25px;
  }

  .wedding-contact-form__decorative-element-{{ ai_gen_id }} {
    text-align: center;
    margin-bottom: 20px;
    color: {{ block.settings.accent_color }};
    font-size: 24px;
  }

  @media screen and (max-width: 749px) {
    .wedding-contact-form-{{ ai_gen_id }} {
      padding: {{ block.settings.form_padding | divided_by: 1.5 }}px;
    }
    
    .wedding-contact-form__title-{{ ai_gen_id }} {
      font-size: {{ block.settings.title_size | minus: 4 }}px;
    }
    
    .wedding-contact-form__description-{{ ai_gen_id }},
    .wedding-contact-form__label-{{ ai_gen_id }},
    .wedding-contact-form__input-{{ ai_gen_id }},
    .wedding-contact-form__textarea-{{ ai_gen_id }},
    .wedding-contact-form__button-{{ ai_gen_id }} {
      font-size: {{ block.settings.text_size | minus: 1 }}px;
    }
  }
{% endstyle %}

<div class="wedding-contact-form-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="wedding-contact-form__decorative-element-{{ ai_gen_id }}">
    ❦
  </div>
  
  <div class="wedding-contact-form__header-{{ ai_gen_id }}">
    <h2 class="wedding-contact-form__title-{{ ai_gen_id }}">{{ block.settings.form_title }}</h2>
    <div class="wedding-contact-form__description-{{ ai_gen_id }}">{{ block.settings.form_description }}</div>
  </div>
  
  <div class="wedding-contact-form__divider-{{ ai_gen_id }}"></div>

  {% form 'contact' %}
    <div class="wedding-contact-form__fields-{{ ai_gen_id }}">
      <div class="wedding-contact-form__field-{{ ai_gen_id }}">
        <label for="ContactName-{{ ai_gen_id }}" class="wedding-contact-form__label-{{ ai_gen_id }}">{{ block.settings.name_label }}</label>
        <input 
          type="text" 
          id="ContactName-{{ ai_gen_id }}" 
          name="contact[name]" 
          class="wedding-contact-form__input-{{ ai_gen_id }}" 
          value="{% if form.name %}{{ form.name }}{% endif %}" 
          required 
          autocomplete="name"
          aria-required="true"
        >
        <div class="wedding-contact-form__error-{{ ai_gen_id }}" data-error-for="ContactName-{{ ai_gen_id }}">
          {{ block.settings.name_error }}
        </div>
      </div>

      <div class="wedding-contact-form__field-{{ ai_gen_id }}">
        <label for="ContactEmail-{{ ai_gen_id }}" class="wedding-contact-form__label-{{ ai_gen_id }}">{{ block.settings.email_label }}</label>
        <input 
          type="email" 
          id="ContactEmail-{{ ai_gen_id }}" 
          name="contact[email]" 
          class="wedding-contact-form__input-{{ ai_gen_id }}" 
          value="{% if form.email %}{{ form.email }}{% endif %}" 
          required 
          autocomplete="email"
          aria-required="true"
          spellcheck="false"
          autocapitalize="off"
        >
        <div class="wedding-contact-form__error-{{ ai_gen_id }}" data-error-for="ContactEmail-{{ ai_gen_id }}">
          {{ block.settings.email_error }}
        </div>
      </div>

      <div class="wedding-contact-form__field-{{ ai_gen_id }}">
        <label for="ContactMessage-{{ ai_gen_id }}" class="wedding-contact-form__label-{{ ai_gen_id }}">{{ block.settings.message_label }}</label>
        <textarea 
          id="ContactMessage-{{ ai_gen_id }}" 
          name="contact[body]" 
          class="wedding-contact-form__textarea-{{ ai_gen_id }}" 
          required
          aria-required="true"
        >{% if form.body %}{{ form.body }}{% endif %}</textarea>
        <div class="wedding-contact-form__error-{{ ai_gen_id }}" data-error-for="ContactMessage-{{ ai_gen_id }}">
          {{ block.settings.message_error }}
        </div>
      </div>

      <button type="submit" class="wedding-contact-form__button-{{ ai_gen_id }}">
        {{ block.settings.button_text }}
      </button>
    </div>

    <div class="wedding-contact-form__success-{{ ai_gen_id }}" {% if form.posted_successfully? %}style="display: block;"{% endif %}>
      {{ block.settings.success_message }}
    </div>

    {% if form.errors %}
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          {% for field in form.errors %}
            var errorElement = document.querySelector('[data-error-for="Contact{{ field | capitalize }}-{{ ai_gen_id }}"]');
            if (errorElement) {
              errorElement.style.display = 'block';
            }
          {% endfor %}
        });
      </script>
    {% endif %}
  {% endform %}
</div>

<script>
  (function() {
    var formElement = document.querySelector('.wedding-contact-form-{{ ai_gen_id }}');
    
    if (formElement) {
      var inputs = formElement.querySelectorAll('.wedding-contact-form__input-{{ ai_gen_id }}, .wedding-contact-form__textarea-{{ ai_gen_id }}');
      
      inputs.forEach(function(input) {
        input.addEventListener('invalid', function(event) {
          event.preventDefault();
          var errorElement = document.querySelector('[data-error-for="' + this.id + '"]');
          if (errorElement) {
            errorElement.style.display = 'block';
            this.style.borderColor = '{{ block.settings.error_color }}';
          }
        });
        
        input.addEventListener('input', function() {
          var errorElement = document.querySelector('[data-error-for="' + this.id + '"]');
          if (errorElement) {
            errorElement.style.display = 'none';
            this.style.borderColor = '{{ block.settings.input_border_color }}';
          }
        });
      });
    }
  })();
</script>

{% schema %}
{
  "name": "Wedding Contact Form",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Form Content"
    },
    {
      "type": "text",
      "id": "form_title",
      "label": "Form title",
      "default": "Contact Us"
    },
    {
      "type": "richtext",
      "id": "form_description",
      "label": "Form description",
      "default": "<p>We'd love to hear from you about your wedding plans. Fill out the form below and we'll get back to you as soon as possible.</p>"
    },
    {
      "type": "text",
      "id": "name_label",
      "label": "Name field label",
      "default": "Your Name"
    },
    {
      "type": "text",
      "id": "email_label",
      "label": "Email field label",
      "default": "Your Email"
    },
    {
      "type": "text",
      "id": "message_label",
      "label": "Message field label",
      "default": "Your Message"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Submit button text",
      "default": "Send Message"
    },
    {
      "type": "text",
      "id": "success_message",
      "label": "Success message",
      "default": "Thank you for your message! We'll get back to you soon."
    },
    {
      "type": "header",
      "content": "Validation Messages"
    },
    {
      "type": "text",
      "id": "name_error",
      "label": "Name field error",
      "default": "Please enter your name"
    },
    {
      "type": "text",
      "id": "email_error",
      "label": "Email field error",
      "default": "Please enter a valid email address"
    },
    {
      "type": "text",
      "id": "message_error",
      "label": "Message field error",
      "default": "Please enter your message"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "form_background",
      "label": "Form background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#555555"
    },
    {
      "type": "color",
      "id": "accent_color",
      "label": "Accent color",
      "default": "#d8b4b4"
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Input background",
      "default": "#f9f9f9"
    },
    {
      "type": "color",
      "id": "input_border_color",
      "label": "Input border color",
      "default": "#e0e0e0"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#d8b4b4"
    },
    {
      "type": "color",
      "id": "button_hover_background",
      "label": "Button hover background",
      "default": "#c9a0a0"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "error_color",
      "label": "Error color",
      "default": "#d85050"
    },
    {
      "type": "color",
      "id": "success_background",
      "label": "Success message background",
      "default": "#f0f7f0"
    },
    {
      "type": "color",
      "id": "success_text_color",
      "label": "Success message text color",
      "default": "#4a8a4a"
    },
    {
      "type": "header",
      "content": "Layout & Styling"
    },
    {
      "type": "range",
      "id": "form_padding",
      "min": 20,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Form padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "form_border_radius",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Form border radius",
      "default": 10
    },
    {
      "type": "range",
      "id": "input_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Input border radius",
      "default": 6
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Button border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "title_size",
      "min": 16,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Title font size",
      "default": 28
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Text font size",
      "default": 16
    },
    {
      "type": "select",
      "id": "button_alignment",
      "label": "Button alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Right"
        }
      ],
      "default": "center"
    }
  ],
  "presets": [
    {
      "name": "Wedding Contact Form"
    }
  ]
}
{% endschema %}